#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将JSON数据导入到Excel表格的脚本
"""

import json
import pandas as pd
from datetime import datetime
import os

def load_json_data(file_path):
    """
    从文件加载JSON数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None

def json_to_excel(json_file_path, excel_file_path=None):
    """
    将JSON数据转换为Excel文件
    
    Args:
        json_file_path: JSON文件路径
        excel_file_path: 输出的Excel文件路径，如果为None则自动生成
    """
    
    # 加载JSON数据
    json_data = load_json_data(json_file_path)
    if json_data is None:
        return False
    
    # 检查数据结构
    if 'data' not in json_data or 'data' not in json_data['data']:
        print("JSON数据结构不正确")
        return False
    
    # 提取订单数据
    orders_data = json_data['data']['data']
    
    if not orders_data:
        print("没有找到订单数据")
        return False
    
    # 转换为DataFrame
    df = pd.DataFrame(orders_data)
    
    # 如果没有指定输出文件名，自动生成
    if excel_file_path is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        excel_file_path = f"订单数据_{timestamp}.xlsx"
    
    try:
        # 导出到Excel
        with pd.ExcelWriter(excel_file_path, engine='openpyxl') as writer:
            # 主数据表
            df.to_excel(writer, sheet_name='订单数据', index=False)
            
            # 创建数据统计表
            stats_data = {
                '统计项': ['总记录数', '总页数', '响应状态', '响应消息'],
                '值': [
                    json_data['data']['rows'],
                    json_data['data']['pages'], 
                    json_data['code'],
                    json_data['message']
                ]
            }
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='数据统计', index=False)
        
        print(f"数据已成功导出到: {excel_file_path}")
        print(f"共导出 {len(df)} 条记录")
        return True
        
    except Exception as e:
        print(f"导出Excel失败: {e}")
        return False

def json_to_csv(json_file_path, csv_file_path=None):
    """
    将JSON数据转换为CSV文件
    
    Args:
        json_file_path: JSON文件路径
        csv_file_path: 输出的CSV文件路径，如果为None则自动生成
    """
    
    # 加载JSON数据
    json_data = load_json_data(json_file_path)
    if json_data is None:
        return False
    
    # 检查数据结构
    if 'data' not in json_data or 'data' not in json_data['data']:
        print("JSON数据结构不正确")
        return False
    
    # 提取订单数据
    orders_data = json_data['data']['data']
    
    if not orders_data:
        print("没有找到订单数据")
        return False
    
    # 转换为DataFrame
    df = pd.DataFrame(orders_data)
    
    # 如果没有指定输出文件名，自动生成
    if csv_file_path is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_file_path = f"订单数据_{timestamp}.csv"
    
    try:
        # 导出到CSV
        df.to_csv(csv_file_path, index=False, encoding='utf-8-sig')
        
        print(f"数据已成功导出到: {csv_file_path}")
        print(f"共导出 {len(df)} 条记录")
        return True
        
    except Exception as e:
        print(f"导出CSV失败: {e}")
        return False

def preview_data(json_file_path, num_rows=5):
    """
    预览数据
    
    Args:
        json_file_path: JSON文件路径
        num_rows: 预览行数
    """
    
    # 加载JSON数据
    json_data = load_json_data(json_file_path)
    if json_data is None:
        return
    
    # 检查数据结构
    if 'data' not in json_data or 'data' not in json_data['data']:
        print("JSON数据结构不正确")
        return
    
    # 提取订单数据
    orders_data = json_data['data']['data']
    
    if not orders_data:
        print("没有找到订单数据")
        return
    
    # 转换为DataFrame
    df = pd.DataFrame(orders_data)
    
    print("=== 数据概览 ===")
    print(f"总记录数: {len(df)}")
    print(f"字段数: {len(df.columns)}")
    print(f"字段列表: {list(df.columns)}")
    
    print(f"\n=== 前 {num_rows} 行数据 ===")
    print(df.head(num_rows).to_string())

if __name__ == "__main__":
    # 输入文件路径
    json_file = "新建 文本文档 (2).txt"
    
    if not os.path.exists(json_file):
        print(f"文件不存在: {json_file}")
        exit(1)
    
    print("请选择操作:")
    print("1. 预览数据")
    print("2. 导出到Excel")
    print("3. 导出到CSV")
    print("4. 同时导出Excel和CSV")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    if choice == "1":
        preview_data(json_file)
    elif choice == "2":
        json_to_excel(json_file)
    elif choice == "3":
        json_to_csv(json_file)
    elif choice == "4":
        json_to_excel(json_file)
        json_to_csv(json_file)
    else:
        print("无效选择")
