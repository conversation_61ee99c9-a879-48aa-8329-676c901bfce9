#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速将JSON数据转换为Excel的脚本
"""

import json
import pandas as pd
from datetime import datetime

# 读取JSON数据
with open("新建 文本文档 (2).txt", 'r', encoding='utf-8') as f:
    json_data = json.load(f)

# 提取订单数据
orders_data = json_data['data']['data']

# 转换为DataFrame
df = pd.DataFrame(orders_data)

# 生成文件名
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
excel_file = f"订单数据_{timestamp}.xlsx"

# 导出到Excel
with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
    # 主数据表
    df.to_excel(writer, sheet_name='订单数据', index=False)
    
    # 数据统计表
    stats_data = {
        '统计项': ['总记录数', '总页数', '响应状态', '响应消息'],
        '值': [
            json_data['data']['rows'],
            json_data['data']['pages'], 
            json_data['code'],
            json_data['message']
        ]
    }
    stats_df = pd.DataFrame(stats_data)
    stats_df.to_excel(writer, sheet_name='数据统计', index=False)

print(f"✅ 数据已成功导出到: {excel_file}")
print(f"📊 共导出 {len(df)} 条记录")
print(f"📋 包含 {len(df.columns)} 个字段")

# 显示字段列表
print("\n字段列表:")
for i, col in enumerate(df.columns, 1):
    print(f"{i:2d}. {col}")
